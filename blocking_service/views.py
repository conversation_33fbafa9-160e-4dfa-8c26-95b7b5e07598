from django.contrib.messages import SUCCESS, add_message
from django.db import transaction
from django.forms import formset_factory
from django.http import FileResponse, HttpResponseRedirect
from django.shortcuts import render
from django.urls import reverse_lazy
from django.views.generic import View, FormView, UpdateView, DetailView
from django.views.generic.detail import SingleObjectMixin
from django_filters.views import FilterView
from django_tables2 import SingleTableView

from blocking_service.filters import (
    BlockListFilter, PersonListFilter, LegalListFilter, UnblockPersonListFilter,
    UnblockLegalListFilter,
)
from blocking_service.forms import (
    Block<PERSON>reateForm, UnblockPersonCreateForm, UnblockLegalCreateForm,
    NumberForm, NumberFormSet, ConfirmForm,
)
from blocking_service.models import (
    Prescription, ObjectPrescription, PrescriptionType, BlockStatus
)
from blocking_service.tables import (
    BlockListTable, PersonListTable, LegalListTable, UnblockPersonListTable,
    UnblockLegalListTable,
)
from blocking_service.tasks import (
    get_info_by_numbers, send_block_requests, send_unlock_person_requests,
    send_unlock_legal_entity_requests
)
from roles.decorators import check_permission


class BlockListView(FilterView, SingleTableView):
    model = Prescription
    table_class = BlockListTable
    filterset_class = BlockListFilter
    template_name = 'ord/block_list.html'
    queryset = Prescription.objects \
        .filter(prescription_type=PrescriptionType.BLOCK.value) \
        .select_related('author') \
        .prefetch_related('number_prescription_through',
                          'number_prescription_through__number') \
        .defer('file')
    paginate_by = 25
    ordering = ['-_date_created']

    @check_permission('ord_block_list')
    def get(self, request, *args, **kwargs):
        return super(BlockListView, self).get(request, *args, **kwargs)


class BlockDetailFileView(SingleObjectMixin, View):
    model = Prescription

    @check_permission('ord_block_list')
    def get(self, request, *args, **kwargs):
        prescription = self.get_object()
        response = FileResponse(prescription.file)
        response['Content-Type'] = 'application/x-binary'
        response['Content-Disposition'] = \
            'attachment; filename="{}"'.format(prescription.file_name)
        return response


class BlockCreateView(View):
    NumberNotBlockFormSet = formset_factory(NumberForm, NumberFormSet, extra=0)
    NumberBlockFormSet = formset_factory(NumberForm, NumberFormSet)

    @check_permission('ord_block_create')
    def get(self, request):
        block_form = BlockCreateForm(prefix='block')
        number_formset = self.NumberNotBlockFormSet(prefix='number')
        number_block_formset = self.NumberBlockFormSet(prefix='number-block')
        return render(request, 'ord/block_create.html', {
            'block_form': block_form,
            'number_formset': number_formset,
            'number_block_formset': number_block_formset,
        })

    @check_permission('ord_block_create')
    def post(self, request):
        block_form = BlockCreateForm(request.POST, request.FILES,
                                     prefix='block')
        number_formset = self.NumberNotBlockFormSet(request.POST,
                                                    prefix='number')
        number_block_formset = self.NumberBlockFormSet(
            request.POST, prefix='number-block')

        if (block_form.is_valid() and number_formset.is_valid()
                and number_block_formset.is_valid()):
            with transaction.atomic():
                block = block_form.save(commit=False)
                block.author = request.user
                block.save()
                block.add_number_list(
                    self._prepare_formset(number_block_formset),
                    BlockStatus.BLOCKED.value
                )
                block.add_number_list(
                    self._prepare_formset(number_formset),
                    BlockStatus.UNBLOCKED.value
                )

            add_message(request, SUCCESS, 'Представление сохранено')
            get_info_by_numbers.delay(block.id)
            return HttpResponseRedirect(reverse_lazy('ord:block_list'))

        return render(request, 'ord/block_create.html', {
            'block_form': block_form,
            'number_formset': number_formset,
            'number_block_formset': number_block_formset,
        })

    def _prepare_formset(self, formset):
        return [f.cleaned_data['phone_number'] for f in formset if f.is_valid()]


class BlockConfirmView(FormView, UpdateView):
    model = Prescription
    form_class = ConfirmForm
    template_name = 'ord/block_confirm.html'
    success_url = reverse_lazy('ord:block_list')
    queryset = Prescription.objects.defer('file')

    def form_valid(self, form):
        response = super().form_valid(form)
        add_message(self.request, SUCCESS, 'Запрос на блокировку отправлен')
        send_block_requests.delay(self.object.id)
        return response

    @check_permission('ord_block_confirm')
    def post(self, request, *args, **kwargs):
        return super(BlockConfirmView, self).post(request, *args, **kwargs)

    @check_permission('ord_block_confirm')
    def get(self, request, *args, **kwargs):
        return super(BlockConfirmView, self).get(request, *args, **kwargs)


class UnblockPersonListView(FilterView, SingleTableView):
    model = Prescription
    table_class = UnblockPersonListTable
    filterset_class = UnblockPersonListFilter
    template_name = 'ord/unblock_person_list.html'
    queryset = Prescription.objects \
        .select_related('author') \
        .prefetch_related('number_prescription_through',
                          'number_prescription_through__number') \
        .filter(prescription_type=PrescriptionType.UNBLOCK_PERSON.value) \
        .defer('file')
    paginate_by = 25
    ordering = ['-_date_created']

    @check_permission('ord_unblock_person_list')
    def get(self, request, *args, **kwargs):
        return super(UnblockPersonListView, self).get(request, *args, **kwargs)


class UnblockPersonDetailFileView(SingleObjectMixin, View):
    model = Prescription

    @check_permission('ord_unblock_person_list')
    def get(self, request, *args, **kwargs):
        prescription = self.get_object()
        response = FileResponse(prescription.file)
        response['Content-Type'] = 'application/x-binary'
        response['Content-Disposition'] = \
            'attachment; filename="{}"'.format(prescription.file_name)
        return response


class UnblockPersonCreateView(View):
    NumberUnblockFormSet = formset_factory(NumberForm, NumberFormSet, extra=0)

    @check_permission('ord_unblock_person_create')
    def get(self, request):
        unblock_form = UnblockPersonCreateForm(prefix='unblock')
        number_unblock_formset = self.NumberUnblockFormSet(
            prefix='number-unblock')
        return render(request, 'ord/unblock_person_create.html', {
            'unblock_form': unblock_form,
            'number_unblock_formset': number_unblock_formset,
        })

    @check_permission('ord_unblock_person_create')
    def post(self, request):
        unblock_form = UnblockPersonCreateForm(request.POST, request.FILES,
                                               prefix='unblock')
        number_unblock_formset = self.NumberUnblockFormSet(
            request.POST, prefix='number-unblock')

        if unblock_form.is_valid() and number_unblock_formset.is_valid():
            with transaction.atomic():
                unblock = unblock_form.save(commit=False)
                unblock.author = request.user
                unblock.save()
                number_list = [f.cleaned_data['phone_number']
                               for f in number_unblock_formset
                               if f.is_valid()]
                unblock.add_number_list(number_list,
                                        BlockStatus.UNBLOCKED.value)

            add_message(self.request, SUCCESS, 'Запрос на разблокировку создан')
            send_unlock_person_requests.delay(unblock.id)
            return HttpResponseRedirect(reverse_lazy('ord:unblock_person_list'))

        return render(request, 'ord/unblock_person_create.html', {
            'unblock_form': unblock_form,
            'number_unblock_formset': number_unblock_formset,
        })


class UnblockPersonConfirmView(UpdateView):
    model = Prescription
    form_class = ConfirmForm
    template_name = 'ord/unblock_person_confirm.html'
    success_url = reverse_lazy('ord:unblock_person_list')
    queryset = Prescription.objects.defer('file')

    def form_valid(self, form):
        response = super().form_valid(form)
        add_message(self.request, SUCCESS, 'Запрос на разблокировку отправлен')
        return response

    @check_permission('ord_unblock_person_confirm')
    def post(self, request, *args, **kwargs):
        return super(UnblockPersonConfirmView, self).post(
            request, *args, **kwargs)

    @check_permission('ord_unblock_person_confirm')
    def get(self, request, *args, **kwargs):
        return super(UnblockPersonConfirmView, self).get(
            request, *args, **kwargs)


class UnblockLegalListView(FilterView, SingleTableView):
    model = Prescription
    table_class = UnblockLegalListTable
    filterset_class = UnblockLegalListFilter
    template_name = 'ord/unblock_legal_list.html'
    queryset = Prescription.objects \
        .select_related('author') \
        .prefetch_related('number_prescription_through',
                          'number_prescription_through__number') \
        .filter(prescription_type=PrescriptionType.UNBLOCK_LEGAL.value) \
        .defer('file')
    paginate_by = 25
    ordering = ['-_date_created']

    @check_permission('ord_unblock_legal_list')
    def get(self, request, *args, **kwargs):
        return super(UnblockLegalListView, self).get(request, *args, **kwargs)


class UnblockLegalDetailFileView(SingleObjectMixin, View):
    model = Prescription

    @check_permission('ord_unblock_legal_list')
    def get(self, request, *args, **kwargs):
        prescription = self.get_object()
        response = FileResponse(prescription.file)
        response['Content-Type'] = 'application/x-binary'
        response['Content-Disposition'] = \
            'attachment; filename="{}"'.format(prescription.file_name)
        return response


class UnblockLegalCreateView(View):
    NumberUnblockFormSet = formset_factory(NumberForm, NumberFormSet, extra=0)

    @check_permission('ord_unblock_legal_create')
    def get(self, request):
        unblock_form = UnblockLegalCreateForm(prefix='unblock')
        number_unblock_formset = self.NumberUnblockFormSet(
            prefix='number-unblock')
        return render(request, 'ord/unblock_legal_create.html', {
            'unblock_form': unblock_form,
            'number_unblock_formset': number_unblock_formset,
        })

    @check_permission('ord_unblock_legal_create')
    def post(self, request):
        unblock_form = UnblockLegalCreateForm(request.POST, request.FILES,
                                               prefix='unblock')
        number_unblock_formset = self.NumberUnblockFormSet(
            request.POST, prefix='number-unblock')

        if unblock_form.is_valid() and number_unblock_formset.is_valid():
            with transaction.atomic():
                unblock = unblock_form.save(commit=False)
                unblock.author = request.user
                unblock.save()
                number_list = [f.cleaned_data['phone_number']
                               for f in number_unblock_formset
                               if f.is_valid()]
                unblock.add_number_list(number_list,
                                        BlockStatus.UNBLOCKED.value)

            add_message(self.request, SUCCESS, 'Заявка на разблокировку создан')
            send_unlock_legal_entity_requests.delay(unblock.id)
            return HttpResponseRedirect(reverse_lazy('ord:unblock_legal_list'))

        return render(request, 'ord/unblock_legal_create.html', {
            'unblock_form': unblock_form,
            'number_unblock_formset': number_unblock_formset,
        })


class UnblockLegalConfirmView(UpdateView):
    model = Prescription
    form_class = ConfirmForm
    template_name = 'ord/unblock_legal_confirm.html'
    success_url = reverse_lazy('ord:unblock_legal_list')
    queryset = Prescription.objects.defer('file')

    def form_valid(self, form):
        response = super().form_valid(form)
        add_message(self.request, SUCCESS, 'Запрос на разблокировку отправлен')
        return response

    @check_permission('ord_unblock_legal_confirm')
    def post(self, request, *args, **kwargs):
        return super(UnblockLegalConfirmView, self).post(
            request, *args, **kwargs)

    @check_permission('ord_unblock_legal_confirm')
    def get(self, request, *args, **kwargs):
        return super(UnblockLegalConfirmView, self).get(
            request, *args, **kwargs)


class PersonListView(FilterView, SingleTableView):
    model = ObjectPrescription
    table_class = PersonListTable
    filterset_class = PersonListFilter
    template_name = 'ord/person_list.html'
    paginate_by = 25
    queryset = ObjectPrescription.objects \
        .filter(object_of_prescription__person__isnull=False) \
        .select_related('prescription', 'prescription__author') \
        .prefetch_related('object_of_prescription__person', 'prescription__numbers') \
        .exclude(prescription__prescription_type=PrescriptionType.UNBLOCK_LEGAL.value)
    ordering = ['-prescription___date_created']

    @check_permission('ord_personal_list')
    def get(self, request, *args, **kwargs):
        return super(PersonListView, self).get(request, *args, **kwargs)


class PersonDetailView(DetailView):
    model = ObjectPrescription
    template_name = 'ord/person_detail.html'

    @check_permission('ord_personal_list')
    def get(self, request, *args, **kwargs):
        return super(PersonDetailView, self).get(request, *args, **kwargs)

    @staticmethod
    def __group_by_operator(entries):
        result = {}
        for entry in entries:
            operator = entry.provider
            number = entry.phone_number
            if operator not in result:
                result[operator] = []
            result[operator].append(number)
        return result

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['prescription_type'] = PrescriptionType.dict()
        context["numbers"] = self.__group_by_operator(
            context['object'].prescription.numbers.all())
        return context


class LegalListView(FilterView, SingleTableView):
    model = ObjectPrescription
    table_class = LegalListTable
    filterset_class = LegalListFilter
    template_name = 'ord/legal_list.html'
    paginate_by = 25
    queryset = ObjectPrescription.objects \
        .filter(object_of_prescription__legal_entity__isnull=False) \
        .select_related('prescription', 'prescription__author') \
        .prefetch_related('object_of_prescription__legal_entity', 'prescription__numbers') \
        .exclude(prescription__prescription_type=PrescriptionType.UNBLOCK_PERSON.value)
    ordering = ['-prescription___date_created']

    @check_permission('ord_legal_list')
    def get(self, request, *args, **kwargs):
        return super(LegalListView, self).get(request, *args, **kwargs)
