from datetime import datetime

import django_tables2 as tables
from django.urls import reverse
from django.utils.formats import date_format
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from . import models
from .models import BlockStatus, PrescriptionStatus, PrescriptionType, \
    PrescriptionHistoryStatus


class BaseActionButtonColumn(tables.Column):
    def __init__(self, action_url_name=None, btn_class='', btn_name='',
                 icon_class='', disabled_condition=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.action_url_name = action_url_name
        self.btn_name = btn_name
        self.btn_class = btn_class
        self.icon_class = icon_class
        self.disabled_condition = disabled_condition or (lambda record: False)

    def render(self, record, value):
        disabled = 'disabled' if self.disabled_condition(record) else ''
        href = '#' if disabled else reverse(self.action_url_name,
                                            kwargs={'pk': value})
        icon_html = '<i class="{}"></i>'.format(self.icon_class) if self.icon_class else ''
        return format_html(
            '<a class="btn {} btn-xs" href="{}" {}>'
            '{}{}</a>',
            self.btn_class, href, disabled, icon_html, self.btn_name
        )


class ManyToManyColumn(tables.Column):
    def __init__(self, field_name, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.field_name = field_name

    def render(self, value):
        return mark_safe(''.join([
            '<p>{}</p>'.format(getattr(v, self.field_name, "")) for v in value.all()
        ]))


class BaseTable(tables.Table):
    class Meta:
        attrs = {'class': 'data table no-margin'}
        template = 'django_tables2/bootstrap.html'


class BlockListTable(BaseTable):
    ud_number = tables.Column(
        verbose_name='№ УД (материала проверки)',
    )
    registration_number = tables.Column(
        verbose_name='Регистрационный №',
    )
    block_number_list = tables.Column(
        accessor='number_prescription_through',
        verbose_name='Абонентский номер (номера), подлежащий блокировке',
        orderable=False,
    )
    number_list = tables.Column(
        accessor='number_prescription_through',
        verbose_name='Абонентский номер (номера), не подлежащий блокировке',
        orderable=False,
    )
    id = tables.Column(
        verbose_name='id',
    )
    scenario = tables.Column(
        verbose_name='Вид применяемых ограничений',
    )
    source = tables.Column(
        verbose_name='источник',
    )
    source_structural_unit = tables.Column(
        verbose_name='Наименования структурного подразделения органа, выдавшего представление',
    )
    _date_created = tables.Column(
        verbose_name='дата создания',
    )
    date_complete = tables.Column(
        verbose_name='дата исполнения',
    )
    status = tables.Column(
        verbose_name='статус',
    )
    file_button = tables.Column(
        accessor="id",
        empty_values=(),
        verbose_name='рdf_файл',
        orderable=False,
    )
    author = tables.Column(
        verbose_name='Исполнитель',
    )
    comment = tables.Column(
        verbose_name='комментарий',
    )
    block_button = BaseActionButtonColumn(
        accessor='id',
        verbose_name='',
        action_url_name='ord:block_confirm',
        btn_name='Заблокировать',
        btn_class='btn-warning',
        disabled_condition=lambda r: not r.status == PrescriptionStatus.INFO_RECEIVED.value,
        orderable=False
    )

    def before_render(self, request):
        self.request = request
        self.columns.hide('block_button')
        if request.user.check_permission_list(['ord_block_confirm']):
            self.columns.show('block_button')

    @staticmethod
    def render_status(record, value):
        return value if record.status in PrescriptionStatus.humanize_value_list() else '—'

    @staticmethod
    def render_block_number_list(record, value):
        phone_number_list = [
            number_prescription_through.number.phone_number
            for number_prescription_through in value.all()
            if number_prescription_through.block_status == BlockStatus.BLOCKED.value
        ]
        return mark_safe(', '.join(phone_number_list))

    @staticmethod
    def render_number_list(record, value):
        phone_number_list = [
            number_prescription_through.number.phone_number
            for number_prescription_through in value.all()
            if number_prescription_through.block_status == BlockStatus.BLOCKED.value
        ]
        return mark_safe(', '.join(phone_number_list))

    @staticmethod
    def render_file_button(record, value):
        href = reverse('ord:block_detail_file_view', kwargs={'pk': value})
        return format_html(
            '<a class="btn btn-primary btn-xs" href="{}" download>'
            '<i class="fa fa-download"></i></a>', href)

    class Meta(BaseTable.Meta):
        model = models.Prescription
        fields = [
            'ud_number',
            'registration_number',
            'block_number_list',
            'number_list',
            'id',
            'scenario',
            'source',
            'source_structural_unit',
            '_date_created',
            'date_complete',
            'status',
            'file_button',
            'author',
            'comment',
        ]


class UnblockPersonListTable(BaseTable):
    registration_number = tables.Column(
        verbose_name='Регистрационный №',
    )
    unblock_number_list = tables.Column(
        accessor='number_prescription_through',
        verbose_name='Абонентский номер (номера), подлежащие блокировке',
        orderable=False,
    )
    id = tables.Column(
        verbose_name='id',
    )
    full_name = tables.Column(
        accessor='extra',
        verbose_name='Ф.И.О. абонента',
    )
    birth_date = tables.Column(
        accessor='extra',
        verbose_name='Дата рождения',
    )
    document_number = tables.Column(
        accessor='extra',
        verbose_name='Идентификационный номер (номер документа, удостоверяющего личность)',
    )
    scenario = tables.Column(
        verbose_name='Вид возобновления услуг',
    )
    source = tables.Column(
        verbose_name='источник',
    )
    source_structural_unit = tables.Column(
        verbose_name='Наименования структурного подразделения органа, выдавшего представление',
    )
    _date_created = tables.Column(
        verbose_name='дата создания',
    )
    date_complete = tables.Column(
        verbose_name='дата исполнения',
    )
    status = tables.Column(
        verbose_name='статус',
    )
    file_button = tables.Column(
        accessor="id",
        empty_values=(),
        verbose_name='рdf_файл',
        orderable=False,
    )
    author = tables.Column(
        verbose_name='Исполнитель',
    )
    comment = tables.Column(
        verbose_name='комментарий',
    )
    unblock_person_button = BaseActionButtonColumn(
        accessor='id',
        verbose_name='',
        action_url_name='ord:unblock_person_confirm',
        btn_name='Разблокировать',
        btn_class='btn-success',
        disabled_condition=lambda r: not r.status == PrescriptionStatus.INFO_RECEIVED.value,
        orderable=False
    )

    def before_render(self, request):
        self.request = request
        self.columns.hide('unblock_person_button')
        if request.user.check_permission_list(['ord_unblock_person_confirm']):
            self.columns.show('unblock_person_button')

    @staticmethod
    def render_status(record, value):
        return value if record.status in PrescriptionStatus.humanize_value_list() else '—'

    @staticmethod
    def render_full_name(record, value):
        return format_html(
            '{} {} {}'.format(
                value.get('surname', ''),
                value.get('name', ''),
                value.get('patronymic', '')
            ).strip()
        )

    @staticmethod
    def render_birth_date(record, value):
        birth_date = value.get('birth_date')
        if birth_date:
            try:
                birth_date = datetime.strptime(birth_date, '%Y-%m-%d')
            except ValueError:
                return birth_date
            return format_html(date_format(birth_date, use_l10n=True))
        return ''

    @staticmethod
    def render_document_number(record, value):
        return format_html(value.get('document_number', ''))

    @staticmethod
    def render_unblock_number_list(record, value):
        phone_number_list = [
            number_prescription_through.number.phone_number
            for number_prescription_through in value.all()
            if number_prescription_through.block_status == BlockStatus.UNBLOCKED.value
        ]
        return mark_safe(', '.join(phone_number_list))

    @staticmethod
    def render_file_button(record, value):
        href = reverse('ord:unblock_person_detail_file_view', kwargs={'pk': value})
        return format_html(
            '<a class="btn btn-primary btn-xs" href="{}" download>'
            '<i class="fa fa-download"></i></a>', href)

    class Meta(BaseTable.Meta):
        model = models.Prescription
        fields = [
            'registration_number',
            'unblock_number_list',
            'id',
            'full_name',
            'birth_date',
            'document_number',
            'scenario',
            'source',
            'source_structural_unit',
            '_date_created',
            'date_complete',
            'status',
            'file_button',
            'author',
            'comment',
            'unblock_person_button',
        ]


class UnblockLegalListTable(BaseTable):
    registration_number = tables.Column(
        verbose_name='Регистрационный №',
    )
    unblock_number_list = tables.Column(
        accessor='number_prescription_through',
        verbose_name='Абонентский номер (номера), подлежащие блокировке',
        orderable=False,
    )
    id = tables.Column(
        verbose_name='id',
    )
    name = tables.Column(
        accessor='extra__name',
        verbose_name='Наименование организации',
    )
    unp = tables.Column(
        accessor='extra__unp',
        verbose_name='УНП',
    )
    scenario = tables.Column(
        verbose_name='Вид возобновления услуг',
    )
    source = tables.Column(
        verbose_name='источник',
    )
    source_structural_unit = tables.Column(
        verbose_name='Наименования структурного подразделения органа, выдавшего представление',
    )
    _date_created = tables.Column(
        verbose_name='дата создания',
    )
    date_complete = tables.Column(
        verbose_name='дата исполнения',
    )
    status = tables.Column(
        verbose_name='статус',
    )
    file_button = tables.Column(
        accessor="id",
        empty_values=(),
        verbose_name='рdf_файл',
        orderable=False,
    )
    author = tables.Column(
        verbose_name='Исполнитель',
    )
    comment = tables.Column(
        verbose_name='комментарий',
    )
    unblock_legal_button = BaseActionButtonColumn(
        accessor='id',
        verbose_name='',
        action_url_name='ord:unblock_legal_confirm',
        btn_name='Разблокировать',
        btn_class='btn-success',
        disabled_condition=lambda r: not r.status == PrescriptionStatus.INFO_RECEIVED.value,
        orderable=False
    )

    def before_render(self, request):
        self.request = request
        self.columns.hide('unblock_legal_button')
        if request.user.check_permission_list(['ord_unblock_legal_confirm']):
            self.columns.show('unblock_legal_button')

    @staticmethod
    def render_status(record, value):
        return value if record.status in PrescriptionStatus.humanize_value_list() else '—'

    @staticmethod
    def render_unblock_number_list(record, value):
        phone_number_list = [
            number_prescription_through.number.phone_number
            for number_prescription_through in value.all()
            if number_prescription_through.block_status == BlockStatus.UNBLOCKED.value
        ]
        return mark_safe(', '.join(phone_number_list))

    @staticmethod
    def render_file_button(record, value):
        href = reverse('ord:unblock_legal_detail_file_view', kwargs={'pk': value})
        return format_html(
            '<a class="btn btn-primary btn-xs" href="{}" download>'
            '<i class="fa fa-download"></i></a>', href)

    class Meta(BaseTable.Meta):
        model = models.Prescription
        fields = [
            'registration_number',
            'unblock_number_list',
            'id',
            'name',
            'unp',
            'scenario',
            'source',
            'source_structural_unit',
            '_date_created',
            'date_complete',
            'status',
            'file_button',
            'author',
            'comment',
            'unblock_legal_button',
        ]


class PersonListTable(BaseTable):
    full_name = tables.Column(
        accessor='object_of_prescription.person.full_name',
        order_by=(
            'object_of_prescription.person.surname',
            'object_of_prescription.person.name',
            'object_of_prescription.person.patronymic',
        ),
        verbose_name='Ф.И.О. абонента',
    )
    birth_date = tables.Column(
        accessor='object_of_prescription.person.birth_date',
        verbose_name='Дата рождения',
    )
    document_number = tables.Column(
        accessor='object_of_prescription.person.document_number',
        verbose_name='Личный номер (номер паспорта)',
    )
    history_status = tables.Column(
        accessor='prescription.history_status',
        verbose_name='Статус',
        orderable=False,
    )
    block_number_list = tables.Column(
        accessor='prescription',
        verbose_name='Абонентский номер (номера), указанные в представлении',
        orderable=False,
    )
    block_author = tables.Column(
        accessor='prescription',
        verbose_name='УЛ, внесшее представление',
    )
    block_registration_number = tables.Column(
        accessor='prescription',
        verbose_name='Рег. № представления',
    )
    block_ud_number = tables.Column(
        accessor='prescription',
        verbose_name='№ УД (материала проверки)',
    )
    block_date_created = tables.Column(
        accessor='prescription',
        verbose_name='Дата ввода ограничений',
    )
    block_scenario = tables.Column(
        accessor='prescription',
        verbose_name='Тип ограничений',
    )
    unblock_author = tables.Column(
        accessor='prescription',
        verbose_name='УЛ, внесшее решение',
    )
    unblock_registration_number = tables.Column(
        accessor='prescription',
        verbose_name='Рег. № решения',
    )
    restrictions_end_date = tables.Column(
        accessor='prescription',
        verbose_name='Дата снятия ограничений',
    )
    unblock_scenario = tables.Column(
        accessor='prescription',
        verbose_name='Тип разрешений',
    )
    unblock_number_list = tables.Column(
        accessor='prescription',
        verbose_name='Абонентский номер (номера), указанные в решении',
        orderable=False,
    )
    detail_button = tables.Column(
        empty_values=(),
        accessor='id',
        verbose_name='',
        orderable=False
    )

    @staticmethod
    def render_block_number_list(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            phone_number_list = [number.phone_number for number in value.numbers.all()]
            return mark_safe(', '.join(phone_number_list))
        return '—'

    @staticmethod
    def render_history_status(record, value):
        return PrescriptionHistoryStatus.get_humanized(value)

    @staticmethod
    def render_block_author(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            return value.author
        return '—'

    @staticmethod
    def render_block_registration_number(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            return value.registration_number
        return '—'

    @staticmethod
    def render_block_ud_number(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            return value.ud_number
        return '—'

    @staticmethod
    def render_block_date_created(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            return value._date_created
        return '—'

    @staticmethod
    def render_block_scenario(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            return value.get_scenario_display()
        return '—'

    @staticmethod
    def render_unblock_author(record, value):
        if value.prescription_type == PrescriptionType.UNBLOCK_PERSON.value:
            return value.author
        return '—'

    @staticmethod
    def render_unblock_registration_number(record, value):
        if value.prescription_type == PrescriptionType.UNBLOCK_PERSON.value:
            return value.registration_number
        return '—'

    @staticmethod
    def render_restrictions_end_date(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            return value.restrictions_end_date or '—'
        return '—'

    @staticmethod
    def render_unblock_scenario(record, value):
        if value.prescription_type == PrescriptionType.UNBLOCK_PERSON.value:
            return value.get_scenario_display()
        return '—'

    @staticmethod
    def render_unblock_number_list(record, value):
        if value.prescription_type == PrescriptionType.UNBLOCK_PERSON.value:
            phone_number_list = [number.phone_number for number in value.numbers.all()]
            return mark_safe(', '.join(phone_number_list))
        return '—'

    @staticmethod
    def render_detail_button(record, value):
        href = reverse('ord:person_detail', kwargs={'pk': value})
        return format_html('<a href="{}"><i class="fa fa-eye"></i></a>', href)

    class Meta(BaseTable.Meta):
        model = models.ObjectPrescription
        fields = [
            'full_name',
            'birth_date',
            'document_number',
            'history_status',
            'block_number_list',
            'block_author',
            'block_registration_number',
            'block_ud_number',
            'block_date_created',
            'block_scenario',
            'unblock_author',
            'unblock_registration_number',
            'restrictions_end_date',
            'unblock_scenario',
            'unblock_number_list',
        ]


class LegalListTable(BaseTable):
    name = tables.Column(
        accessor='object_of_prescription.legal_entity.name',
        verbose_name='Наименование организации',
    )
    unp = tables.Column(
        accessor='object_of_prescription.legal_entity.unp',
        verbose_name='УНП',
    )
    history_status = tables.Column(
        accessor='prescription.history_status',
        verbose_name='Статус',
        orderable=False,
    )
    block_number_list = tables.Column(
        accessor='prescription',
        verbose_name='Абонентский номер (номера), указанные в представлении',
        orderable=False,
    )
    block_author = tables.Column(
        accessor='prescription',
        verbose_name='УЛ, внесшее представление',
    )
    block_registration_number = tables.Column(
        accessor='prescription',
        verbose_name='Рег. № представления',
    )
    block_ud_number = tables.Column(
        accessor='prescription',
        verbose_name='№ УД (материала проверки)',
    )
    block_date_created = tables.Column(
        accessor='prescription',
        verbose_name='Дата ввода ограничений',
    )
    block_scenario = tables.Column(
        accessor='prescription',
        verbose_name='Тип ограничений',
    )
    unblock_author = tables.Column(
        accessor='prescription',
        verbose_name='УЛ, внесшее решение',
    )
    unblock_registration_number = tables.Column(
        accessor='prescription',
        verbose_name='Рег. № решения',
    )
    restrictions_end_date = tables.Column(
        accessor='prescription',
        verbose_name='Дата снятия ограничений',
    )
    unblock_scenario = tables.Column(
        accessor='prescription',
        verbose_name='Тип разрешений',
    )
    unblock_number_list = tables.Column(
        accessor='prescription',
        verbose_name='Абонентский номер (номера), указанные в решении',
        orderable=False,
    )

    @staticmethod
    def render_block_number_list(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            phone_number_list = [number.phone_number for number in value.numbers.all()]
            return mark_safe(', '.join(phone_number_list))
        return '—'

    @staticmethod
    def render_history_status(record, value):
        return PrescriptionHistoryStatus.get_humanized(value)

    @staticmethod
    def render_block_author(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            return value.author
        return '—'

    @staticmethod
    def render_block_registration_number(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            return value.registration_number
        return '—'

    @staticmethod
    def render_block_ud_number(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            return value.ud_number
        return '—'

    @staticmethod
    def render_block_date_created(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            return value._date_created
        return '—'

    @staticmethod
    def render_block_scenario(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            return value.get_scenario_display()
        return '—'

    @staticmethod
    def render_unblock_author(record, value):
        if value.prescription_type == PrescriptionType.UNBLOCK_LEGAL.value:
            return value.author
        return '—'

    @staticmethod
    def render_unblock_registration_number(record, value):
        if value.prescription_type == PrescriptionType.UNBLOCK_LEGAL.value:
            return value.registration_number
        return '—'

    @staticmethod
    def render_restrictions_end_date(record, value):
        if value.prescription_type == PrescriptionType.BLOCK.value:
            return value.restrictions_end_date or '—'
        return '—'

    @staticmethod
    def render_unblock_scenario(record, value):
        if value.prescription_type == PrescriptionType.UNBLOCK_LEGAL.value:
            return value.get_scenario_display()
        return '—'

    @staticmethod
    def render_unblock_number_list(record, value):
        if value.prescription_type == PrescriptionType.UNBLOCK_LEGAL.value:
            phone_number_list = [number.phone_number for number in value.numbers.all()]
            return mark_safe(', '.join(phone_number_list))
        return '—'

    class Meta(BaseTable.Meta):
        model = models.LegalEntity
        fields = [
            'name',
            'unp',
            'history_status',
            'block_number_list',
            'block_author',
            'block_registration_number',
            'block_ud_number',
            'block_date_created',
            'block_scenario',
            'unblock_author',
            'unblock_registration_number',
            'restrictions_end_date',
            'unblock_scenario',
            'unblock_number_list',
        ]
