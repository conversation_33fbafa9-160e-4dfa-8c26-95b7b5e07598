import datetime
from enum import Enum

from django.contrib.postgres.fields import J<PERSON><PERSON><PERSON>
from django.db import models

from core.models import NULL_BLANK, Provider
from roles.models import User


SCENARIO_BLOCK = (
    (1, 'Сценарий 1 – блокируются все номера, указанные в представлении, сведения о пользователе (пользователях) и '
        'заблокированных номерах помещаются в Реестр. Не вводится запрет на заключение новых договоров'),
    (2, 'Сценарий 2 – блокируются все номера, указанные в представлении, сведения о пользователе (пользователях) и '
        'заблокированных номерах помещаются в Реестр. Вводится запрет на заключение новых договоров'),
    (3, 'Сценарий 3 - блокируются все номера, сведения о пользователе и всех заблокированных номерах помещаются в '
        'Реестр. Вводится запрет на заключение новых договоров'),
    (4, 'Сценарий 4 - блокируются все номера, сведения о пользователе и всех заблокированных номерах помещаются в '
        'Реестр. Не вводится запрет на заключение новых договоров'),
    (5, 'Сценарий 5 - блокируются все номера, за исключением абонентского номера (номеров), указанных в представлении, '
        'сведения о пользователе и всех заблокированных номерах помещается в Реестр. Вводится запрет на заключение '
        'новых договоров'),
    (6, 'Сценарий 6 - блокируются все номера, за исключением абонентского номера (номеров), указанных в представлении, '
        'сведения о пользователе и всех заблокированных номерах помещается в Реестр. Не вводится запрет на заключение '
        'новых договоров'),
)

SCENARIO_UNBLOCK_PERSON = (
    (7, 'Сценарий 1 – операторам направляются уведомления о разблокировке абонентских номеров, снимается запрет на '
        'заключение новых договоров на оказания услуг электросвязи'),
    (8, 'Сценарий 2 – операторам направляются уведомления о разблокировке абонентских номеров'),
    (9, 'Сценарий 3 – снимается запрет на заключение новых договоров на оказания услуг электросвязи3'),
)

SCENARIO_UNBLOCK_LEGAL = (
    (10, 'Сценарий 1 – операторам направляются уведомления о разблокировке абонентских номеров'),
)


SCENARIO = SCENARIO_BLOCK + SCENARIO_UNBLOCK_PERSON + SCENARIO_UNBLOCK_LEGAL

class PrescriptionType(Enum):
    BLOCK = 1
    UNBLOCK_PERSON = 2
    UNBLOCK_LEGAL = 3

    @classmethod
    def dict(cls):
        return {type_.name: type_.value for type_ in cls}

    @classmethod
    def choices(cls):
        return (
            (cls.BLOCK.value, 'Предписание на блокировку'),
            (cls.UNBLOCK_PERSON.value, 'Решение на разблокировку физических лиц'),
            (cls.UNBLOCK_LEGAL.value, 'Решение на разблокировку юридических лиц'),
        )


PRESCRIPTION_TYPE = PrescriptionType.choices()


class BlockStatus(Enum):
    BLOCKED = 1
    UNBLOCKED = 2

    @classmethod
    def choices(cls):
        return (
            (cls.BLOCKED.value, 'Заблокирован'),
            (cls.UNBLOCKED.value, 'Не заблокирован'),
        )


SOURCE = (
    ('mvd', 'МВД'),
    ('prokuratura', 'Прокуратура'),
    ('sk', 'СК'),
    ('kgb', 'КГБ'),
)


class PrescriptionStatus(Enum):
    INFO_REQUESTED = 1
    INFO_RECEIVED = 2
    IN_PROCESS = 3
    EXECUTED = 4
    REFUSED = 5
    EXPIRED = 6

    @classmethod
    def choices(cls):
        return (
            (cls.INFO_REQUESTED.value, 'Информация запрошена'),
            (cls.INFO_RECEIVED.value, 'Информация получена'),
            (cls.IN_PROCESS.value, 'В обработке'),
            (cls.EXECUTED.value, 'Исполнено'),
            (cls.REFUSED.value, 'Отклонено'),
            (cls.EXPIRED.value, 'Просрочено'),
        )

    @classmethod
    def humanize_value_list(cls):
        return [cls.EXECUTED.value, cls.REFUSED.value, cls.EXPIRED.value]

    @classmethod
    def humanize_choices(cls):
        return (
            (cls.EXECUTED.value, 'Исполнено'),
            (cls.REFUSED.value, 'Отклонено'),
            (cls.EXPIRED.value, 'Просрочено'),
        )


class PrescriptionHistoryStatus(Enum):
    BLOCKED = 1
    UNACTIVE = 2

    @classmethod
    def choices(cls):
        return (
            (cls.BLOCKED.value, 'Заблокирован'),
            (cls.UNACTIVE.value, 'Неактивен'),
        )

    @classmethod
    def get_humanized(cls, value):
        return dict(cls.choices())[value]


STATUS = PrescriptionStatus.choices()

BLOCK_STATUS = BlockStatus.choices()

REQUEST_TYPE = (
    (1, 'Запрос на получение данных'),
    (2, 'Запрос на блокировку'),
    (3, 'Запрос на разблокировку'),
)


class ENTITY_STATUS(str, Enum):
    STOPED = 'STOPED'
    RESUMED = 'RESUMED'


ENTITY_STATUS_CHOICES = {
    ENTITY_STATUS.STOPED.value: 'Заблокирован',
    ENTITY_STATUS.RESUMED.value: 'Неактивен'
}


class Prescription(models.Model):
    prescription_type = models.IntegerField(
        choices=PRESCRIPTION_TYPE
    )
    registration_number = models.IntegerField(
        verbose_name='Исходящий регистрационный № уполномоченного органа'
    )
    # для предписаний на блокировку, для решений NULL
    ud_number = models.IntegerField(
        verbose_name='Номер УД (материала проверки) указанный в представлении',
        **NULL_BLANK
    )
    source = models.CharField(
        verbose_name='Орган, выдавший предписание (МВД, Прокуратура, СК, КГБ)',
        choices=SOURCE,
        max_length=50,
    )
    source_structural_unit = models.CharField(
        verbose_name='Наименования структурного подразделения органа, выдавшего представление',
        max_length=50,
        **NULL_BLANK
    )
    source_phone = models.CharField(
        verbose_name='Номер телефона органа, выдавшего представление',
        max_length=15,
        **NULL_BLANK
    )
    _date_created = models.DateTimeField(
        verbose_name='Дата создания',
        auto_now_add=True,
    )
    date_complete = models.DateTimeField(
        verbose_name='Заполняется автоматически при успешном выполнении',
        **NULL_BLANK
    )
    author = models.ForeignKey(
        to=User,
        verbose_name='Кто «ввел» предписание в систему (в соответствии с учетной записью)',
        blank=False,
        null=True,
        on_delete=models.SET_NULL
    )
    comment = models.CharField(
        verbose_name='Причины отклонения блокировки на стороне оператора',
        max_length=1024,
        **NULL_BLANK
    )
    status = models.IntegerField(
        verbose_name='Исполнено, Отклонено, Просрочено',
        choices=STATUS,
        **NULL_BLANK
    )
    file = models.BinaryField(
        verbose_name='Файл',
    )
    information_received = models.BooleanField(
        verbose_name='Статус получения информации о номерах, если получено то можно блокировать / разблокировать',
        default=False,
    )
    scenario = models.IntegerField(
        verbose_name='В соответствии со сценариями блокировки',
        choices=SCENARIO
    )
    numbers = models.ManyToManyField(
        'Numbers',
        through="NumbersPrescription",
        through_fields=("prescription", "number"),
        related_name="prescription_numbers"
    )
    objects_of_prescription = models.ManyToManyField(
        'ObjectOfPrescription',
        through="ObjectPrescription",
        through_fields=("prescription", "object_of_prescription"),
        related_name="prescription_objects"
    )
    extra = JSONField(
        verbose_name='Дополнительные данные для решений',
        default=dict
    )

    @property
    def history_status(self):
        return PrescriptionHistoryStatus.BLOCKED.value \
            if self.scenario in (2, 3, 5) \
            else PrescriptionHistoryStatus.UNACTIVE.value

    #ToDo переделать на месяцы
    @property
    def restrictions_end_date(self):
        if self.date_complete:
            return self.date_complete + datetime.timedelta(days=90) \
                if self.prescription_type == PrescriptionType.BLOCK.value \
                else self.date_complete
        else:
            return None

    @property
    def file_name(self):
        return '{}.pdf'.format(self.registration_number)

    def add_number_list(self, number_list, block_status):
        existing = set(
            Numbers.objects.filter(phone_number__in=number_list)
            .values_list('phone_number', flat=True)
        )

        new_number_list = [Numbers(phone_number=n) for n in number_list
                           if n not in existing]

        if new_number_list:
            Numbers.objects.bulk_create(new_number_list)

        all_number_list = Numbers.objects.filter(phone_number__in=number_list)

        new_number_prescription = [
            NumbersPrescription(prescription=self, number=number,
                                block_status=block_status)
            for number in all_number_list
        ]
        NumbersPrescription.objects.bulk_create(new_number_prescription)


class PersonAbstract(models.Model):
    surname = models.CharField(
        verbose_name='Фамилия',
        max_length=80,
    )
    name = models.CharField(
        verbose_name='Имя',
        max_length=80,
    )
    patronymic = models.CharField(
        verbose_name='Отчество',
        max_length=80,
    )
    birth_date = models.DateField(
        verbose_name='Дата рождения',
    )
    document_number = models.CharField(
        verbose_name='Номер документа',
        max_length=50,
    )
    personal_number = models.CharField(
        verbose_name='Личный номер',
        max_length=50,
    )

    @property
    def full_name(self):
        return '{} {} {}'.format(self.surname, self.name, self.patronymic)

    class Meta:
        abstract = True


class LegalEntityAbstract(models.Model):
    name = models.CharField(
        verbose_name='Имя',
        max_length=80,
    )
    unp = models.CharField(
        verbose_name='УНП',
        max_length=50,
        blank=False,
        db_index=True
    )

    class Meta:
        abstract = True


class Numbers(models.Model):
    phone_number = models.CharField(
        verbose_name='Номер телефона',
        max_length=15,
    )

    prescriptions = models.ManyToManyField(
        'Prescription',
        through="NumbersPrescription",
        through_fields=("number", "prescription"),
        related_name="number_prescriptions"
    )
    provider = models.ForeignKey(
        to=Provider,
        **NULL_BLANK
    )

    def __str__(self):
        return self.phone_number


class NumbersPrescription(models.Model):
    number = models.ForeignKey(
        to=Numbers,
        related_name='number_prescription_through'
    )
    block_status = models.IntegerField(
        default=1,
        choices=BLOCK_STATUS
    )
    prescription = models.ForeignKey(
        to=Prescription,
        related_name='number_prescription_through'
    )


class ObjectOfPrescription(models.Model):
    numbers = models.ManyToManyField(
        'Numbers',
        through="ObjectNumbers",
        through_fields=("object_number", "number"),
        related_name="objects_of_numbers"
    )


class ObjectPrescription(models.Model):
    object_of_prescription = models.ForeignKey(
        to=ObjectOfPrescription,
    )
    block_status = models.IntegerField(
        default=1,
        choices=BLOCK_STATUS
    )
    prescription = models.ForeignKey(
        to=Prescription
    )


class ObjectNumbers(models.Model):
    object_number = models.ForeignKey(
        to=ObjectOfPrescription,
    )
    number = models.ForeignKey(
        to=Numbers,
    )


class Person(PersonAbstract):

    resident = models.CharField(
        verbose_name='Является ли резидентом Республики Беларусь?',
        choices=(
            ('yes', 'Да'),
            ('no', 'Нет'),
        ),
        max_length=3,
        db_index=True,
        **NULL_BLANK
    )
    document_type = models.CharField(
        verbose_name='Тип документа, удостоверяющего личность',
        max_length=80,
        **NULL_BLANK
    )
    issued_at = models.DateField(
        verbose_name='Даты выдачи документа',
        **NULL_BLANK
    )
    issued_by = models.CharField(
        verbose_name='Гос. орган, выдавший документ',
        max_length=255,
        **NULL_BLANK
    )

    base_object = models.OneToOneField(
        to=ObjectOfPrescription,
        related_name='person',
    )

    @property
    def issued(self):
        return ', '.join([str(v) for v in [self.issued_at, self.issued_by] if v]) or None

    @property
    def status(self):
        unblock_person_prescription = self.base_object.unlock_person_prescription_objects \
            .order_by('-_date_created') \
            .last()
        block_prescription = self.base_object.prescription_objects \
            .order_by('-_date_created') \
            .last()

        if all((unblock_person_prescription, block_prescription)):
            if unblock_person_prescription._date_created > block_prescription._date_created:
                return ENTITY_STATUS.RESUMED
        return ENTITY_STATUS.STOPED



class LegalEntity(LegalEntityAbstract):
    base_object = models.OneToOneField(
        to=ObjectOfPrescription,
        related_name='legal_entity',
    )

    @property
    def status(self):
        unblock_legal_prescription = self.base_object.unlock_legal_entity_prescription_objects \
            .order_by('-_date_created') \
            .last()
        block_prescription = self.base_object.prescription_objects \
            .order_by('-_date_created') \
            .last()

        if all((unblock_legal_prescription, block_prescription)):
            if unblock_legal_prescription._date_created > block_prescription._date_created:
                return ENTITY_STATUS.RESUMED
        return ENTITY_STATUS.STOPED


class RequestToProvider(models.Model):
    provider = models.ForeignKey(
        to=Provider,
    )
    prescription = models.ForeignKey(
        to=Prescription
    )
    request_type = models.IntegerField(
        choices=REQUEST_TYPE
    )
    request_data = models.TextField(
        verbose_name='Данные запроса'
    )
    response_code = models.IntegerField(
        verbose_name='Сетевой статус ответа',
        **NULL_BLANK
    )
    response_data = models.TextField(
        verbose_name='Данные ответа'
    )
    answer_code = models.IntegerField(
        verbose_name='Сетевой статус результирующего запроса',
        **NULL_BLANK
    )
    answer_data = models.TextField(
        verbose_name='Данные результирующего запроса',
        **NULL_BLANK
    )
    request_date = models.DateTimeField(
        verbose_name='Дата/время отправки запроса',
        auto_now_add=True,
    )
    answer_date = models.DateTimeField(
        verbose_name='Дата/время получения результирующего запроса',
        **NULL_BLANK
    )
